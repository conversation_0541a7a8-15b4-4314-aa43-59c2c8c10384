/* CSS Variables for Theme Management */
:root {
    /* Light Theme Colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #ec4899;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: rgba(255, 255, 255, 0.8);
    --bg-modal: rgba(255, 255, 255, 0.95);
    
    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;
    
    /* Border Colors */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: rgba(30, 41, 59, 0.8);
    --bg-modal: rgba(15, 23, 42, 0.95);
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    
    --border-color: #334155;
    --border-hover: #475569;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color var(--transition-normal), color var(--transition-normal);
    overflow-x: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* App Container */
.app-container {
    min-height: 100vh;
    background: var(--gradient-primary);
    position: relative;
}

.app-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Header */
.header {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo i {
    font-size: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-container i {
    position: absolute;
    left: var(--spacing-sm);
    color: var(--text-muted);
    z-index: 1;
}

.search-input {
    padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    width: 300px;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.theme-toggle, .stats-btn {
    padding: var(--spacing-sm);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.theme-toggle:hover, .stats-btn:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    transform: translateY(-2px);
}

/* Main Content */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--spacing-xl);
    min-height: calc(100vh - 80px);
}

/* Sidebar */
.sidebar {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    height: fit-content;
    position: sticky;
    top: 100px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.sidebar-section {
    margin-bottom: var(--spacing-xl);
}

.sidebar-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.category-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
}

.category-item:hover {
    background: var(--bg-tertiary);
    transform: translateX(4px);
}

.category-item.active {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.category-item .count {
    margin-left: auto;
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.add-category-btn {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-md);
}

.add-category-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.quick-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.stat-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* Task Area */
.task-area {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.task-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.task-controls {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
    flex-wrap: wrap;
}

.sort-select, .filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.sort-select:focus, .filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.add-task-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gradient-primary);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: all var(--transition-fast);
}

.add-task-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Progress Bar */
.progress-bar-container {
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-accent);
    border-radius: var(--radius-lg);
    transition: width var(--transition-slow);
    width: 0%;
}

.progress-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 80px;
}

/* Task List */
.task-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    max-height: 600px;
    overflow-y: auto;
    padding-right: var(--spacing-xs);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-muted);
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.empty-state p {
    margin-bottom: var(--spacing-lg);
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-inverse);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--border-hover);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 250px 1fr;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
    }
    
    .search-input {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
    
    .sidebar {
        position: static;
        order: 2;
    }
    
    .task-area {
        order: 1;
    }
    
    .header-content {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .header-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .search-input {
        width: 100%;
        max-width: 300px;
    }
    
    .task-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .task-controls {
        width: 100%;
        justify-content: space-between;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: var(--spacing-sm);
    }

    .main-content {
        padding: var(--spacing-sm);
    }

    .sidebar, .task-area {
        padding: var(--spacing-md);
    }

    .task-controls {
        flex-direction: column;
        width: 100%;
    }

    .sort-select, .filter-select, .add-task-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Task Item Styles */
.task-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    transition: all var(--transition-fast);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.task-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.task-item.completed {
    opacity: 0.7;
    background: var(--bg-secondary);
}

.task-item.completed .task-title {
    text-decoration: line-through;
    color: var(--text-muted);
}

.task-item.overdue {
    border-left: 4px solid var(--error-color);
}

.task-item.high-priority {
    border-left: 4px solid var(--error-color);
}

.task-item.medium-priority {
    border-left: 4px solid var(--warning-color);
}

.task-item.low-priority {
    border-left: 4px solid var(--success-color);
}

.task-header-row {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.task-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    flex-shrink: 0;
    margin-top: 2px;
}

.task-checkbox:hover {
    border-color: var(--primary-color);
}

.task-checkbox.checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.task-content {
    flex: 1;
    min-width: 0;
}

.task-title {
    font-weight: 600;
    font-size: 1rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    word-wrap: break-word;
}

.task-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.5;
}

.task-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    font-size: 0.75rem;
}

.task-category, .task-priority, .task-due-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.task-category {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.task-priority.high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.task-priority.medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.task-priority.low {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.task-due-date {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

.task-due-date.overdue {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.task-actions {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.task-item:hover .task-actions {
    opacity: 1;
}

.task-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
}

.task-action-btn:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    transform: scale(1.1);
}

.task-action-btn.delete:hover {
    background: var(--error-color);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-modal);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: transform var(--transition-normal);
}

.modal-overlay.active .modal {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.close-btn:hover {
    background: var(--error-color);
    color: var(--text-inverse);
}

.modal-body {
    padding: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* Statistics Modal */
.stats-modal {
    max-width: 700px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.stat-card i {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card h4 {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.productivity-chart {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.productivity-chart h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.chart-container {
    height: 200px;
    display: flex;
    align-items: end;
    justify-content: space-between;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) 0;
}

.chart-bar {
    background: var(--gradient-accent);
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
    min-height: 20px;
    flex: 1;
    transition: all var(--transition-normal);
    position: relative;
    cursor: pointer;
}

.chart-bar:hover {
    transform: scaleY(1.1);
    filter: brightness(1.2);
}

.chart-bar::after {
    content: attr(data-day);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: var(--text-inverse);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-fast);
    z-index: 100;
}

.fab:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 100px;
    right: 2rem;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--bg-modal);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 300px;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--primary-color);
}

.toast-icon {
    font-size: 1.25rem;
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--error-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast.info .toast-icon {
    color: var(--primary-color);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.toast-message {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.toast-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Animations */
@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.animate-slide-up {
    animation: slideInUp 0.3s ease-out;
}

.animate-slide-right {
    animation: slideInRight 0.3s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s;
}

/* Drag and Drop */
.task-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.task-list.drag-over {
    background: rgba(99, 102, 241, 0.1);
    border: 2px dashed var(--primary-color);
    border-radius: var(--radius-lg);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }

    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Print styles */
@media print {
    .header-controls,
    .task-actions,
    .fab,
    .modal-overlay,
    .toast-container {
        display: none !important;
    }

    .app-container {
        background: white !important;
    }

    .task-item {
        break-inside: avoid;
        margin-bottom: 1rem;
        border: 1px solid #ccc;
        padding: 1rem;
    }

    .sidebar {
        display: none;
    }

    .main-content {
        grid-template-columns: 1fr;
    }
}

/* Performance optimizations */
.task-item,
.category-item,
.modal {
    will-change: transform;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
.task-list::-webkit-scrollbar {
    width: 6px;
}

.task-list::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

.task-list::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: var(--radius-sm);
}

.task-list::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Loading skeleton animation */
.skeleton {
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Enhanced focus indicators */
.task-checkbox:focus,
.category-item:focus,
.task-action-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Improved mobile touch targets */
@media (max-width: 768px) {
    .task-action-btn,
    .task-checkbox,
    .close-btn {
        min-width: 44px;
        min-height: 44px;
    }

    .category-item {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

/* Dark mode image adjustments */
[data-theme="dark"] img {
    opacity: 0.8;
    filter: brightness(0.8) contrast(1.2);
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .progress-fill,
    .task-item,
    .modal,
    .toast {
        transition: none !important;
        animation: none !important;
    }

    .app-container::before {
        animation: none !important;
    }
}
