// TaskMaster Pro - Advanced Todo App
class TaskMasterPro {
    constructor() {
        this.tasks = JSON.parse(localStorage.getItem('taskmaster-tasks')) || [];
        this.categories = JSON.parse(localStorage.getItem('taskmaster-categories')) || ['personal', 'work', 'shopping'];
        this.currentCategory = 'all';
        this.currentFilter = 'all';
        this.currentSort = 'date';
        this.searchQuery = '';
        this.editingTaskId = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadTheme();
        this.renderTasks();
        this.updateStats();
        this.updateCategoryCounts();
        this.setupKeyboardShortcuts();
        this.showWelcomeMessage();
    }

    setupEventListeners() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());
        
        // Search
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.searchQuery = e.target.value.toLowerCase();
            this.renderTasks();
        });
        
        // Category selection
        document.getElementById('categoryList').addEventListener('click', (e) => {
            const categoryItem = e.target.closest('.category-item');
            if (categoryItem) {
                this.selectCategory(categoryItem.dataset.category);
            }
        });
        
        // Task controls
        document.getElementById('addTaskBtn').addEventListener('click', () => this.openTaskModal());
        document.getElementById('sortSelect').addEventListener('change', (e) => {
            this.currentSort = e.target.value;
            this.renderTasks();
        });
        document.getElementById('filterSelect').addEventListener('change', (e) => {
            this.currentFilter = e.target.value;
            this.renderTasks();
        });
        
        // Modal controls
        document.getElementById('taskForm').addEventListener('submit', (e) => this.handleTaskSubmit(e));
        document.getElementById('statsBtn').addEventListener('click', () => this.openStatsModal());
        
        // Close modals on overlay click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeAllModals();
            }
        });
        
        // Add category button
        document.getElementById('addCategoryBtn').addEventListener('click', () => this.addNewCategory());
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + N: New task
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                this.openTaskModal();
            }
            
            // Escape: Close modals
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
            
            // Ctrl/Cmd + /: Focus search
            if ((e.ctrlKey || e.metaKey) && e.key === '/') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }
        });
    }

    // Theme Management
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('taskmaster-theme', newTheme);
        
        const themeIcon = document.querySelector('#themeToggle i');
        themeIcon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        
        this.showToast('success', 'Theme Changed', `Switched to ${newTheme} mode`);
    }

    loadTheme() {
        const savedTheme = localStorage.getItem('taskmaster-theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        const themeIcon = document.querySelector('#themeToggle i');
        themeIcon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }

    // Task Management
    createTask(taskData) {
        const task = {
            id: this.generateId(),
            title: taskData.title,
            description: taskData.description || '',
            category: taskData.category,
            priority: taskData.priority,
            dueDate: taskData.dueDate || null,
            completed: false,
            createdAt: new Date().toISOString(),
            completedAt: null
        };
        
        this.tasks.unshift(task);
        this.saveTasks();
        this.renderTasks();
        this.updateStats();
        this.updateCategoryCounts();
        
        this.showToast('success', 'Task Created', `"${task.title}" has been added to your list`);
        return task;
    }

    updateTask(taskId, updates) {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            this.tasks[taskIndex] = { ...this.tasks[taskIndex], ...updates };
            this.saveTasks();
            this.renderTasks();
            this.updateStats();
            this.updateCategoryCounts();
            
            this.showToast('info', 'Task Updated', 'Task has been successfully updated');
        }
    }

    deleteTask(taskId) {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            const task = this.tasks[taskIndex];
            this.tasks.splice(taskIndex, 1);
            this.saveTasks();
            this.renderTasks();
            this.updateStats();
            this.updateCategoryCounts();
            
            this.showToast('warning', 'Task Deleted', `"${task.title}" has been removed`);
        }
    }

    toggleTaskComplete(taskId) {
        const task = this.tasks.find(task => task.id === taskId);
        if (task) {
            task.completed = !task.completed;
            task.completedAt = task.completed ? new Date().toISOString() : null;
            
            this.saveTasks();
            this.renderTasks();
            this.updateStats();
            this.updateCategoryCounts();
            
            const message = task.completed ? 'Task completed! 🎉' : 'Task marked as pending';
            this.showToast(task.completed ? 'success' : 'info', 'Status Changed', message);
            
            if (task.completed) {
                this.animateTaskCompletion(taskId);
            }
        }
    }

    animateTaskCompletion(taskId) {
        const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
        if (taskElement) {
            taskElement.classList.add('animate-bounce');
            setTimeout(() => {
                taskElement.classList.remove('animate-bounce');
            }, 1000);
        }
    }

    // Category Management
    selectCategory(category) {
        this.currentCategory = category;
        
        // Update active category in sidebar
        document.querySelectorAll('.category-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');
        
        // Update header
        const categoryNames = {
            all: 'All Tasks',
            personal: 'Personal Tasks',
            work: 'Work Tasks',
            shopping: 'Shopping Tasks'
        };
        document.getElementById('currentCategory').textContent = categoryNames[category] || category;
        
        this.renderTasks();
    }

    addNewCategory() {
        const categoryName = prompt('Enter new category name:');
        if (categoryName && categoryName.trim()) {
            const normalizedName = categoryName.toLowerCase().trim();
            if (!this.categories.includes(normalizedName)) {
                this.categories.push(normalizedName);
                this.saveCategories();
                this.renderCategories();
                this.updateCategoryCounts();
                
                this.showToast('success', 'Category Added', `"${categoryName}" category has been created`);
            } else {
                this.showToast('warning', 'Category Exists', 'This category already exists');
            }
        }
    }

    renderCategories() {
        const categoryList = document.getElementById('categoryList');
        const customCategories = this.categories.filter(cat => !['personal', 'work', 'shopping'].includes(cat));
        
        // Add custom categories to the list
        customCategories.forEach(category => {
            if (!document.querySelector(`[data-category="${category}"]`)) {
                const categoryItem = document.createElement('div');
                categoryItem.className = 'category-item';
                categoryItem.dataset.category = category;
                categoryItem.innerHTML = `
                    <i class="fas fa-folder"></i>
                    <span>${category.charAt(0).toUpperCase() + category.slice(1)}</span>
                    <span class="count" id="${category}Count">0</span>
                `;
                categoryList.appendChild(categoryItem);
            }
        });
        
        // Update task category select options
        const taskCategorySelect = document.getElementById('taskCategory');
        taskCategorySelect.innerHTML = '';
        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
            taskCategorySelect.appendChild(option);
        });
    }

    // Filtering and Sorting
    getFilteredTasks() {
        let filteredTasks = [...this.tasks];
        
        // Apply category filter
        if (this.currentCategory !== 'all') {
            filteredTasks = filteredTasks.filter(task => task.category === this.currentCategory);
        }
        
        // Apply status filter
        switch (this.currentFilter) {
            case 'pending':
                filteredTasks = filteredTasks.filter(task => !task.completed);
                break;
            case 'completed':
                filteredTasks = filteredTasks.filter(task => task.completed);
                break;
            case 'overdue':
                filteredTasks = filteredTasks.filter(task => 
                    !task.completed && task.dueDate && new Date(task.dueDate) < new Date()
                );
                break;
        }
        
        // Apply search filter
        if (this.searchQuery) {
            filteredTasks = filteredTasks.filter(task =>
                task.title.toLowerCase().includes(this.searchQuery) ||
                task.description.toLowerCase().includes(this.searchQuery)
            );
        }
        
        // Apply sorting
        filteredTasks.sort((a, b) => {
            switch (this.currentSort) {
                case 'priority':
                    const priorityOrder = { high: 3, medium: 2, low: 1 };
                    return priorityOrder[b.priority] - priorityOrder[a.priority];
                case 'name':
                    return a.title.localeCompare(b.title);
                case 'category':
                    return a.category.localeCompare(b.category);
                case 'date':
                default:
                    return new Date(b.createdAt) - new Date(a.createdAt);
            }
        });
        
        return filteredTasks;
    }

    // Utility Functions
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = date - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays < 0) {
            return `${Math.abs(diffDays)} days overdue`;
        } else if (diffDays === 0) {
            return 'Due today';
        } else if (diffDays === 1) {
            return 'Due tomorrow';
        } else {
            return `Due in ${diffDays} days`;
        }
    }

    isOverdue(dateString) {
        if (!dateString) return false;
        return new Date(dateString) < new Date();
    }

    saveTasks() {
        localStorage.setItem('taskmaster-tasks', JSON.stringify(this.tasks));
    }

    saveCategories() {
        localStorage.setItem('taskmaster-categories', JSON.stringify(this.categories));
    }

    showWelcomeMessage() {
        if (this.tasks.length === 0) {
            setTimeout(() => {
                this.showToast('info', 'Welcome to TaskMaster Pro!', 'Start by creating your first task');
            }, 1000);
        }
    }

    // Rendering Functions
    renderTasks() {
        const taskList = document.getElementById('taskList');
        const emptyState = document.getElementById('emptyState');
        const filteredTasks = this.getFilteredTasks();

        if (filteredTasks.length === 0) {
            taskList.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }

        taskList.style.display = 'flex';
        emptyState.style.display = 'none';

        taskList.innerHTML = filteredTasks.map(task => this.createTaskHTML(task)).join('');

        // Add event listeners to task items
        this.attachTaskEventListeners();
        this.updateProgressBar();
    }

    createTaskHTML(task) {
        const isOverdue = !task.completed && this.isOverdue(task.dueDate);
        const dueDateText = task.dueDate ? this.formatDate(task.dueDate) : '';

        return `
            <div class="task-item ${task.completed ? 'completed' : ''} ${isOverdue ? 'overdue' : ''} ${task.priority}-priority animate-slide-up"
                 data-task-id="${task.id}" draggable="true">
                <div class="task-header-row">
                    <div class="task-checkbox ${task.completed ? 'checked' : ''}" onclick="app.toggleTaskComplete('${task.id}')">
                        ${task.completed ? '<i class="fas fa-check"></i>' : ''}
                    </div>
                    <div class="task-content">
                        <div class="task-title">${this.escapeHtml(task.title)}</div>
                        ${task.description ? `<div class="task-description">${this.escapeHtml(task.description)}</div>` : ''}
                        <div class="task-meta">
                            <div class="task-category">
                                <i class="fas fa-tag"></i>
                                ${task.category}
                            </div>
                            <div class="task-priority ${task.priority}">
                                <i class="fas fa-flag"></i>
                                ${task.priority}
                            </div>
                            ${task.dueDate ? `
                                <div class="task-due-date ${isOverdue ? 'overdue' : ''}">
                                    <i class="fas fa-calendar"></i>
                                    ${dueDateText}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="task-actions">
                        <button class="task-action-btn" onclick="app.editTask('${task.id}')" title="Edit Task">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="task-action-btn delete" onclick="app.deleteTask('${task.id}')" title="Delete Task">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    attachTaskEventListeners() {
        const taskItems = document.querySelectorAll('.task-item');

        taskItems.forEach(item => {
            // Drag and drop
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', item.dataset.taskId);
                item.classList.add('dragging');
            });

            item.addEventListener('dragend', () => {
                item.classList.remove('dragging');
            });
        });

        // Drop zone
        const taskList = document.getElementById('taskList');
        taskList.addEventListener('dragover', (e) => {
            e.preventDefault();
            taskList.classList.add('drag-over');
        });

        taskList.addEventListener('dragleave', () => {
            taskList.classList.remove('drag-over');
        });

        taskList.addEventListener('drop', (e) => {
            e.preventDefault();
            taskList.classList.remove('drag-over');
            // Handle reordering logic here if needed
        });
    }

    updateProgressBar() {
        const totalTasks = this.tasks.filter(task =>
            this.currentCategory === 'all' || task.category === this.currentCategory
        ).length;
        const completedTasks = this.tasks.filter(task =>
            task.completed && (this.currentCategory === 'all' || task.category === this.currentCategory)
        ).length;

        const percentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        document.getElementById('progressFill').style.width = `${percentage}%`;
        document.getElementById('progressText').textContent = `${percentage}% Complete`;
    }

    updateStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(task => task.completed).length;
        const pending = total - completed;

        document.getElementById('totalTasks').textContent = total;
        document.getElementById('completedTasks').textContent = completed;
        document.getElementById('pendingTasks').textContent = pending;

        // Update stats modal
        document.getElementById('statsTotalTasks').textContent = total;
        document.getElementById('statsCompletedTasks').textContent = completed;
        document.getElementById('statsPendingTasks').textContent = pending;

        const overdue = this.tasks.filter(task =>
            !task.completed && this.isOverdue(task.dueDate)
        ).length;
        document.getElementById('statsOverdueTasks').textContent = overdue;
    }

    updateCategoryCounts() {
        const counts = {};

        // Initialize counts
        this.categories.forEach(category => {
            counts[category] = 0;
        });
        counts.all = this.tasks.length;

        // Count tasks by category
        this.tasks.forEach(task => {
            if (counts.hasOwnProperty(task.category)) {
                counts[task.category]++;
            }
        });

        // Update UI
        Object.keys(counts).forEach(category => {
            const countElement = document.getElementById(`${category}Count`);
            if (countElement) {
                countElement.textContent = counts[category];
            }
        });
    }

    // Modal Management
    openTaskModal(taskId = null) {
        this.editingTaskId = taskId;
        const modal = document.getElementById('taskModal');
        const modalTitle = document.getElementById('modalTitle');
        const form = document.getElementById('taskForm');

        if (taskId) {
            const task = this.tasks.find(t => t.id === taskId);
            if (task) {
                modalTitle.textContent = 'Edit Task';
                document.getElementById('taskTitle').value = task.title;
                document.getElementById('taskDescription').value = task.description;
                document.getElementById('taskCategory').value = task.category;
                document.getElementById('taskPriority').value = task.priority;
                if (task.dueDate) {
                    document.getElementById('taskDueDate').value = task.dueDate.slice(0, 16);
                }
            }
        } else {
            modalTitle.textContent = 'Add New Task';
            form.reset();
        }

        modal.classList.add('active');
        document.getElementById('taskTitle').focus();
    }

    closeTaskModal() {
        document.getElementById('taskModal').classList.remove('active');
        this.editingTaskId = null;
    }

    openStatsModal() {
        this.renderProductivityChart();
        document.getElementById('statsModal').classList.add('active');
    }

    closeStatsModal() {
        document.getElementById('statsModal').classList.remove('active');
    }

    closeAllModals() {
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.classList.remove('active');
        });
        this.editingTaskId = null;
    }

    handleTaskSubmit(e) {
        e.preventDefault();

        const formData = {
            title: document.getElementById('taskTitle').value.trim(),
            description: document.getElementById('taskDescription').value.trim(),
            category: document.getElementById('taskCategory').value,
            priority: document.getElementById('taskPriority').value,
            dueDate: document.getElementById('taskDueDate').value || null
        };

        if (!formData.title) {
            this.showToast('error', 'Validation Error', 'Task title is required');
            return;
        }

        if (this.editingTaskId) {
            this.updateTask(this.editingTaskId, formData);
        } else {
            this.createTask(formData);
        }

        this.closeTaskModal();
    }

    editTask(taskId) {
        this.openTaskModal(taskId);
    }

    // Toast Notifications
    showToast(type, title, message) {
        const toastContainer = document.getElementById('toastContainer');
        const toastId = this.generateId();

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.id = toastId;
        toast.innerHTML = `
            <div class="toast-icon">
                <i class="fas ${this.getToastIcon(type)}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="app.closeToast('${toastId}')">
                <i class="fas fa-times"></i>
            </button>
        `;

        toastContainer.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove after 5 seconds
        setTimeout(() => this.closeToast(toastId), 5000);
    }

    getToastIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    closeToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }
    }

    // Productivity Chart
    renderProductivityChart() {
        const chartContainer = document.getElementById('productivityChart');
        const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        const today = new Date();
        const weekData = [];

        // Generate data for the past week
        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);

            const completedTasks = this.tasks.filter(task => {
                if (!task.completedAt) return false;
                const completedDate = new Date(task.completedAt);
                return completedDate.toDateString() === date.toDateString();
            }).length;

            weekData.push({
                day: days[date.getDay() === 0 ? 6 : date.getDay() - 1],
                count: completedTasks
            });
        }

        const maxCount = Math.max(...weekData.map(d => d.count), 1);

        chartContainer.innerHTML = weekData.map(data => `
            <div class="chart-bar"
                 style="height: ${(data.count / maxCount) * 100}%"
                 data-day="${data.day}"
                 title="${data.count} tasks completed">
            </div>
        `).join('');
    }

    // Data Export/Import
    exportData() {
        const data = {
            tasks: this.tasks,
            categories: this.categories,
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `taskmaster-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showToast('success', 'Data Exported', 'Your tasks have been exported successfully');
    }

    importData(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);

                if (data.tasks && Array.isArray(data.tasks)) {
                    this.tasks = data.tasks;
                    this.saveTasks();
                }

                if (data.categories && Array.isArray(data.categories)) {
                    this.categories = data.categories;
                    this.saveCategories();
                    this.renderCategories();
                }

                this.renderTasks();
                this.updateStats();
                this.updateCategoryCounts();

                this.showToast('success', 'Data Imported', 'Your tasks have been imported successfully');
            } catch (error) {
                this.showToast('error', 'Import Failed', 'Invalid file format');
            }
        };
        reader.readAsText(file);
    }

    // Utility Functions
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Advanced Features
    duplicateTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (task) {
            const duplicatedTask = {
                ...task,
                id: this.generateId(),
                title: `${task.title} (Copy)`,
                completed: false,
                createdAt: new Date().toISOString(),
                completedAt: null
            };

            this.tasks.unshift(duplicatedTask);
            this.saveTasks();
            this.renderTasks();
            this.updateStats();
            this.updateCategoryCounts();

            this.showToast('success', 'Task Duplicated', 'Task has been duplicated successfully');
        }
    }

    bulkComplete(category = null) {
        let tasksToComplete = this.tasks.filter(task => !task.completed);

        if (category && category !== 'all') {
            tasksToComplete = tasksToComplete.filter(task => task.category === category);
        }

        tasksToComplete.forEach(task => {
            task.completed = true;
            task.completedAt = new Date().toISOString();
        });

        this.saveTasks();
        this.renderTasks();
        this.updateStats();
        this.updateCategoryCounts();

        this.showToast('success', 'Bulk Complete', `${tasksToComplete.length} tasks marked as completed`);
    }

    bulkDelete(category = null) {
        if (!confirm('Are you sure you want to delete these tasks? This action cannot be undone.')) {
            return;
        }

        let tasksToDelete = [...this.tasks];

        if (category && category !== 'all') {
            tasksToDelete = tasksToDelete.filter(task => task.category === category);
        }

        const deleteCount = tasksToDelete.length;
        this.tasks = this.tasks.filter(task => !tasksToDelete.includes(task));

        this.saveTasks();
        this.renderTasks();
        this.updateStats();
        this.updateCategoryCounts();

        this.showToast('warning', 'Bulk Delete', `${deleteCount} tasks have been deleted`);
    }

    // Search and Filter Enhancements
    clearSearch() {
        document.getElementById('searchInput').value = '';
        this.searchQuery = '';
        this.renderTasks();
    }

    // Accessibility Features
    announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = message;

        document.body.appendChild(announcement);
        setTimeout(() => document.body.removeChild(announcement), 1000);
    }
}

// Global functions for HTML onclick handlers
window.openTaskModal = () => app.openTaskModal();
window.closeTaskModal = () => app.closeTaskModal();
window.closeStatsModal = () => app.closeStatsModal();

// Initialize the app
const app = new TaskMasterPro();

// Additional Event Listeners for Advanced Features
document.addEventListener('DOMContentLoaded', () => {
    // Add export/import functionality
    const exportBtn = document.createElement('button');
    exportBtn.className = 'btn-secondary';
    exportBtn.innerHTML = '<i class="fas fa-download"></i> Export Data';
    exportBtn.onclick = () => app.exportData();

    const importBtn = document.createElement('button');
    importBtn.className = 'btn-secondary';
    importBtn.innerHTML = '<i class="fas fa-upload"></i> Import Data';
    importBtn.onclick = () => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            if (e.target.files[0]) {
                app.importData(e.target.files[0]);
            }
        };
        input.click();
    };

    // Add buttons to stats modal footer
    const statsModal = document.querySelector('#statsModal .modal-body');
    if (statsModal) {
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = 'display: flex; gap: 1rem; margin-top: 2rem; justify-content: center;';
        buttonContainer.appendChild(exportBtn);
        buttonContainer.appendChild(importBtn);
        statsModal.appendChild(buttonContainer);
    }

    // Add context menu for tasks (right-click)
    document.addEventListener('contextmenu', (e) => {
        const taskItem = e.target.closest('.task-item');
        if (taskItem) {
            e.preventDefault();
            const taskId = taskItem.dataset.taskId;
            showContextMenu(e.clientX, e.clientY, taskId);
        }
    });

    // Close context menu on click elsewhere
    document.addEventListener('click', () => {
        const contextMenu = document.getElementById('contextMenu');
        if (contextMenu) {
            contextMenu.remove();
        }
    });
});

function showContextMenu(x, y, taskId) {
    // Remove existing context menu
    const existingMenu = document.getElementById('contextMenu');
    if (existingMenu) {
        existingMenu.remove();
    }

    const contextMenu = document.createElement('div');
    contextMenu.id = 'contextMenu';
    contextMenu.style.cssText = `
        position: fixed;
        top: ${y}px;
        left: ${x}px;
        background: var(--bg-modal);
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        z-index: 1200;
        min-width: 150px;
        overflow: hidden;
    `;

    const menuItems = [
        { icon: 'fa-edit', text: 'Edit Task', action: () => app.editTask(taskId) },
        { icon: 'fa-copy', text: 'Duplicate', action: () => app.duplicateTask(taskId) },
        { icon: 'fa-check', text: 'Toggle Complete', action: () => app.toggleTaskComplete(taskId) },
        { icon: 'fa-trash', text: 'Delete', action: () => app.deleteTask(taskId), danger: true }
    ];

    menuItems.forEach(item => {
        const menuItem = document.createElement('div');
        menuItem.style.cssText = `
            padding: 0.75rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: ${item.danger ? 'var(--error-color)' : 'var(--text-primary)'};
            transition: background-color 0.15s ease;
        `;
        menuItem.innerHTML = `<i class="fas ${item.icon}"></i> ${item.text}`;

        menuItem.addEventListener('mouseenter', () => {
            menuItem.style.backgroundColor = 'var(--bg-secondary)';
        });

        menuItem.addEventListener('mouseleave', () => {
            menuItem.style.backgroundColor = 'transparent';
        });

        menuItem.addEventListener('click', () => {
            item.action();
            contextMenu.remove();
        });

        contextMenu.appendChild(menuItem);
    });

    document.body.appendChild(contextMenu);

    // Adjust position if menu goes off screen
    const rect = contextMenu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
        contextMenu.style.left = `${x - rect.width}px`;
    }
    if (rect.bottom > window.innerHeight) {
        contextMenu.style.top = `${y - rect.height}px`;
    }
}

// Enhanced keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Only trigger if not typing in an input
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
    }

    switch (e.key.toLowerCase()) {
        case 'a':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                app.bulkComplete(app.currentCategory);
            }
            break;
        case 'd':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                if (confirm('Delete all tasks in current category?')) {
                    app.bulkDelete(app.currentCategory);
                }
            }
            break;
        case 'e':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                app.exportData();
            }
            break;
        case 's':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                app.openStatsModal();
            }
            break;
        case '1':
        case '2':
        case '3':
        case '4':
            const categories = ['all', 'personal', 'work', 'shopping'];
            const categoryIndex = parseInt(e.key) - 1;
            if (categories[categoryIndex]) {
                app.selectCategory(categories[categoryIndex]);
            }
            break;
    }
});

// Auto-save functionality
setInterval(() => {
    if (app.tasks.length > 0) {
        app.saveTasks();
        app.saveCategories();
    }
}, 30000); // Auto-save every 30 seconds

// Performance optimization: Debounced search
let searchTimeout;
document.getElementById('searchInput').addEventListener('input', (e) => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        app.searchQuery = e.target.value.toLowerCase();
        app.renderTasks();
    }, 300);
});

// Add notification permission request
if ('Notification' in window && Notification.permission === 'default') {
    setTimeout(() => {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                app.showToast('success', 'Notifications Enabled', 'You\'ll receive reminders for due tasks');
            }
        });
    }, 5000);
}

// Check for overdue tasks and show notifications
function checkOverdueTasks() {
    if ('Notification' in window && Notification.permission === 'granted') {
        const overdueTasks = app.tasks.filter(task =>
            !task.completed && app.isOverdue(task.dueDate)
        );

        if (overdueTasks.length > 0) {
            new Notification('TaskMaster Pro - Overdue Tasks', {
                body: `You have ${overdueTasks.length} overdue task(s)`,
                icon: '/favicon.ico',
                tag: 'overdue-tasks'
            });
        }
    }
}

// Check for overdue tasks every hour
setInterval(checkOverdueTasks, 3600000);

// Initial check after 10 seconds
setTimeout(checkOverdueTasks, 10000);

console.log('🚀 TaskMaster Pro loaded successfully!');
console.log('💡 Keyboard shortcuts:');
console.log('  Ctrl/Cmd + N: New task');
console.log('  Ctrl/Cmd + /: Focus search');
console.log('  Ctrl/Cmd + A: Complete all tasks');
console.log('  Ctrl/Cmd + S: Show statistics');
console.log('  Ctrl/Cmd + E: Export data');
console.log('  1-4: Switch categories');
console.log('  Escape: Close modals');
