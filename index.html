<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskMaster Pro - Advanced Todo App</title>
    <meta name="description" content="A professional, feature-rich todo application with dark/light themes, categories, priorities, and advanced task management.">
    <meta name="keywords" content="todo, task manager, productivity, organizer">
    <meta name="author" content="TaskMaster Pro">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>✅</text></svg>">

    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-tasks"></i>
                    <h1>TaskMaster Pro</h1>
                </div>
                <div class="header-controls">
                    <div class="search-container">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search tasks..." class="search-input">
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="stats-btn" id="statsBtn" title="View Statistics">
                        <i class="fas fa-chart-bar"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-section">
                    <h3>Categories</h3>
                    <div class="category-list" id="categoryList">
                        <div class="category-item active" data-category="all">
                            <i class="fas fa-list"></i>
                            <span>All Tasks</span>
                            <span class="count" id="allCount">0</span>
                        </div>
                        <div class="category-item" data-category="personal">
                            <i class="fas fa-user"></i>
                            <span>Personal</span>
                            <span class="count" id="personalCount">0</span>
                        </div>
                        <div class="category-item" data-category="work">
                            <i class="fas fa-briefcase"></i>
                            <span>Work</span>
                            <span class="count" id="workCount">0</span>
                        </div>
                        <div class="category-item" data-category="shopping">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Shopping</span>
                            <span class="count" id="shoppingCount">0</span>
                        </div>
                    </div>
                    <button class="add-category-btn" id="addCategoryBtn">
                        <i class="fas fa-plus"></i>
                        Add Category
                    </button>
                </div>

                <div class="sidebar-section">
                    <h3>Quick Stats</h3>
                    <div class="quick-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total</span>
                            <span class="stat-value" id="totalTasks">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Completed</span>
                            <span class="stat-value" id="completedTasks">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Pending</span>
                            <span class="stat-value" id="pendingTasks">0</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Task Area -->
            <section class="task-area">
                <div class="task-header">
                    <h2 id="currentCategory">All Tasks</h2>
                    <div class="task-controls">
                        <select id="sortSelect" class="sort-select">
                            <option value="date">Sort by Date</option>
                            <option value="priority">Sort by Priority</option>
                            <option value="name">Sort by Name</option>
                            <option value="category">Sort by Category</option>
                        </select>
                        <select id="filterSelect" class="filter-select">
                            <option value="all">All Tasks</option>
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="overdue">Overdue</option>
                        </select>
                        <button class="add-task-btn" id="addTaskBtn">
                            <i class="fas fa-plus"></i>
                            Add Task
                        </button>
                    </div>
                </div>

                <div class="progress-bar-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <span class="progress-text" id="progressText">0% Complete</span>
                </div>

                <div class="task-list" id="taskList">
                    <!-- Tasks will be dynamically added here -->
                </div>

                <div class="empty-state" id="emptyState">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>No tasks yet</h3>
                    <p>Create your first task to get started!</p>
                    <button class="btn-primary" onclick="openTaskModal()">
                        <i class="fas fa-plus"></i>
                        Add Your First Task
                    </button>
                </div>
            </section>
        </main>

        <!-- Task Modal -->
        <div class="modal-overlay" id="taskModal">
            <div class="modal">
                <div class="modal-header">
                    <h3 id="modalTitle">Add New Task</h3>
                    <button class="close-btn" onclick="closeTaskModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form class="modal-body" id="taskForm">
                    <div class="form-group">
                        <label for="taskTitle">Task Title *</label>
                        <input type="text" id="taskTitle" required placeholder="Enter task title...">
                    </div>
                    <div class="form-group">
                        <label for="taskDescription">Description</label>
                        <textarea id="taskDescription" placeholder="Enter task description..."></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskCategory">Category</label>
                            <select id="taskCategory">
                                <option value="personal">Personal</option>
                                <option value="work">Work</option>
                                <option value="shopping">Shopping</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="taskPriority">Priority</label>
                            <select id="taskPriority">
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="taskDueDate">Due Date</label>
                        <input type="datetime-local" id="taskDueDate">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary" onclick="closeTaskModal()">Cancel</button>
                        <button type="submit" class="btn-primary" id="saveTaskBtn">
                            <i class="fas fa-save"></i>
                            Save Task
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Statistics Modal -->
        <div class="modal-overlay" id="statsModal">
            <div class="modal stats-modal">
                <div class="modal-header">
                    <h3>Task Statistics</h3>
                    <button class="close-btn" onclick="closeStatsModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <i class="fas fa-tasks"></i>
                            <h4>Total Tasks</h4>
                            <span class="stat-number" id="statsTotalTasks">0</span>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-check-circle"></i>
                            <h4>Completed</h4>
                            <span class="stat-number" id="statsCompletedTasks">0</span>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-clock"></i>
                            <h4>Pending</h4>
                            <span class="stat-number" id="statsPendingTasks">0</span>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h4>Overdue</h4>
                            <span class="stat-number" id="statsOverdueTasks">0</span>
                        </div>
                    </div>
                    <div class="productivity-chart">
                        <h4>Productivity This Week</h4>
                        <div class="chart-container" id="productivityChart">
                            <!-- Chart will be generated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Floating Action Button -->
        <button class="fab" id="fabBtn" onclick="openTaskModal()">
            <i class="fas fa-plus"></i>
        </button>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>
